"""
群发获客控制器
负责处理群发获客页面的业务逻辑
"""

import logging
import re
import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QPushButton, QLabel, QVBoxLayout, QHBoxLayout, QFrame
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QObject
from PySide6.QtGui import QFont, QPixmap

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

# 导入数据库操作和WhatsApp功能
from database.telenumber_operations import TeleNumberOperations
from chat.whatsapp.bulk_login_manager import WhatsAppBulkLoginManager
from chat.whatsapp.bulk_sender import WhatsAppBulkSender

# 导入自定义组件
from gui.widgets.international_phone_input import InternationalPhoneInput
from gui.core.country_code_manager import CountryCodeManager

# 设置日志
logger = logging.getLogger(__name__)

class BulkUISignals(QObject):
    """群发获客UI信号类"""
    login_success = Signal()  # 登录成功信号
    login_error = Signal(str)  # 登录错误信号
    qr_code_received = Signal(str)  # QR码接收信号


class BulkCustomerAcquisitionController:
    """群发获客控制器"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.username = getattr(main_window, 'current_username', 'default_user')  # 获取当前用户名
        self.store_name = "bulk_messaging"  # 群发专用店铺名

        # 创建UI信号对象
        self.ui_signals = BulkUISignals()

        # 数据库操作
        self.telenumber_ops = TeleNumberOperations()

        # WhatsApp登录和发送管理
        self.login_manager = WhatsAppBulkLoginManager(self.username, self.store_name)
        self.bulk_sender = WhatsAppBulkSender(self.login_manager)

        # 获取UI组件引用
        self.message_input_field = main_window.ui.load_pages.message_input_field
        self.phone_numbers_table = main_window.ui.load_pages.phone_numbers_table
        self.add_phone_btn = main_window.ui.load_pages.add_phone_btn
        self.start_bulk_send_btn = main_window.ui.load_pages.start_bulk_send_btn

        # 创建国际电话号码输入组件
        self.country_manager = CountryCodeManager()
        self._setup_international_phone_input()

        # 设置输入提示
        self.message_input_field.setPlaceholderText("输入要群发的消息内容...")

        # 添加新的UI组件
        self._create_login_ui()

        # 设置回调函数
        self._setup_callbacks()

        # 连接信号
        self.connect_signals()

        # 初始化表格
        self.init_table()

        # 加载数据库中的电话号码
        self.load_phone_numbers_from_db()

        # 更新UI状态
        self.update_ui_state()

        # 设置定时器定期更新UI状态
        self.ui_update_timer = QTimer()
        self.ui_update_timer.timeout.connect(self.update_ui_state_timer)
        self.ui_update_timer.start(5000)  # 每5秒更新一次UI状态（减少频率）

        logger.info(f"群发获客控制器初始化完成 - 用户: {self.username}")

    def _setup_international_phone_input(self):
        """设置国际电话号码输入组件"""
        try:
            # 获取原有的电话号码输入框和其父容器
            original_phone_input = self.main_window.ui.load_pages.phone_input_field
            phone_input_frame = self.main_window.ui.load_pages.phone_input_frame
            phone_input_layout = self.main_window.ui.load_pages.phone_input_layout

            logger.info(f"原始输入框: {original_phone_input}")
            logger.info(f"输入框父容器: {phone_input_frame}")
            logger.info(f"输入框布局: {phone_input_layout}")

            # 找到原输入框在布局中的位置
            input_index = -1
            for i in range(phone_input_layout.count()):
                item = phone_input_layout.itemAt(i)
                if item and item.widget() == original_phone_input:
                    input_index = i
                    logger.info(f"找到原输入框在布局中的位置: {input_index}")
                    break

            if input_index == -1:
                logger.error("无法找到电话号码输入框在布局中的位置")
                # 回退到原有输入框
                self.phone_input_field = original_phone_input
                self.phone_input_field.setPlaceholderText("输入手机号码，如：13812345678")
                return

            # 创建国际电话号码输入组件
            self.international_phone_input = InternationalPhoneInput()
            self.international_phone_input.setMinimumHeight(40)  # 设置最小高度

            # 连接信号
            self.international_phone_input.phoneChanged.connect(self.on_phone_input_changed)
            self.international_phone_input.validationChanged.connect(self.on_phone_validation_changed)

            # 替换原有输入框
            phone_input_layout.removeWidget(original_phone_input)
            original_phone_input.hide()
            original_phone_input.setParent(None)  # 完全移除父子关系

            # 插入新的国际电话号码输入组件
            phone_input_layout.insertWidget(input_index, self.international_phone_input)

            # 设置引用（为了兼容性）
            self.phone_input_field = self.international_phone_input

            logger.info("国际电话号码输入组件设置成功")

        except Exception as e:
            logger.error(f"设置国际电话号码输入组件时出错: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 回退到原有输入框
            self.phone_input_field = self.main_window.ui.load_pages.phone_input_field
            self.phone_input_field.setPlaceholderText("输入手机号码，如：13812345678")

    def on_phone_input_changed(self, country_code, local_number):
        """电话号码输入变化回调"""
        # 更新UI状态
        self.update_ui_state()

    def on_phone_validation_changed(self, is_valid, error_message):
        """电话号码验证状态变化回调"""
        # 可以在这里添加实时验证反馈
        pass

    def _create_login_ui(self):
        """创建登录相关的UI组件"""
        try:
            # 获取右侧操作区域的布局
            bulk_right_layout = self.main_window.ui.load_pages.bulk_right_layout

            # 创建登录状态区域
            self.login_frame = QFrame()
            self.login_frame.setObjectName("login_frame")
            self.login_frame.setStyleSheet("""
                QFrame#login_frame {
                    background-color: #21252d;
                    border-radius: 10px;
                    border: 1px solid #3c4454;
                    padding: 15px;
                }
            """)

            self.login_layout = QVBoxLayout(self.login_frame)
            self.login_layout.setSpacing(10)

            # 登录状态标题
            self.login_title = QLabel("WhatsApp登录状态")
            self.login_title.setStyleSheet("color: #fff; font-size: 14pt; font-weight: bold;")
            self.login_layout.addWidget(self.login_title)

            # 状态指示器和按钮布局
            self.login_status_layout = QHBoxLayout()

            # 状态指示灯
            self.status_indicator = QLabel("●")
            self.status_indicator.setStyleSheet("color: #ff4444; font-size: 16pt;")  # 红色表示未登录
            self.login_status_layout.addWidget(self.status_indicator)

            # 状态文本
            self.status_text = QLabel("未登录")
            self.status_text.setStyleSheet("color: #c3ccdf; font-size: 12pt;")
            self.login_status_layout.addWidget(self.status_text)

            self.login_status_layout.addStretch()

            # 登录按钮
            self.login_btn = QPushButton("登录WhatsApp")
            self.login_btn.setObjectName("login_btn")
            self.login_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 11pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #66BB6A;
                }
                QPushButton:pressed {
                    background-color: #388E3C;
                }
                QPushButton:disabled {
                    background-color: #666;
                    color: #999;
                }
            """)
            self.login_status_layout.addWidget(self.login_btn)

            # 退出登录按钮
            self.logout_btn = QPushButton("退出登录")
            self.logout_btn.setObjectName("logout_btn")
            self.logout_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 11pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #ef5350;
                }
                QPushButton:pressed {
                    background-color: #d32f2f;
                }
                QPushButton:disabled {
                    background-color: #666;
                    color: #999;
                }
            """)
            self.logout_btn.setEnabled(False)  # 初始状态禁用
            self.login_status_layout.addWidget(self.logout_btn)

            # 重置状态按钮
            self.reset_status_btn = QPushButton("重置状态")
            self.reset_status_btn.setObjectName("reset_status_btn")
            self.reset_status_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff9800;
                    color: white;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 11pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #ffb74d;
                }
                QPushButton:pressed {
                    background-color: #f57c00;
                }
            """)
            self.login_status_layout.addWidget(self.reset_status_btn)

            self.login_layout.addLayout(self.login_status_layout)

            # 登录提示信息
            self.login_info = QLabel("请先登录WhatsApp才能使用群发功能")
            self.login_info.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            self.login_info.setWordWrap(True)
            self.login_layout.addWidget(self.login_info)

            # 将登录区域插入到操作区域的最前面
            bulk_right_layout.insertWidget(0, self.login_frame)

            logger.info("登录UI组件创建成功")

        except Exception as e:
            logger.error(f"创建登录UI时出错: {str(e)}")

    def _setup_callbacks(self):
        """设置回调函数"""
        # 登录管理器回调（使用线程安全的回调）
        self.login_manager.set_callbacks(
            login_status_callback=self.on_login_status_changed_safe,
            qr_code_callback=self.on_qr_code_received_safe,
            error_callback=self.on_login_error_safe
        )

        # 群发发送器回调
        self.bulk_sender.set_callbacks(
            progress_callback=self.on_send_progress,
            completion_callback=self.on_send_completed,
            error_callback=self.on_send_error
        )

    def connect_signals(self):
        """连接信号到槽函数"""
        # 原有按钮信号
        self.add_phone_btn.clicked.connect(self.add_phone_number)
        self.start_bulk_send_btn.clicked.connect(self.start_bulk_send)

        # 电话号码输入信号（兼容新旧组件）
        if hasattr(self, 'international_phone_input'):
            # 新的国际电话号码输入组件已经在_setup_international_phone_input中连接了信号
            pass
        else:
            # 回退到原有输入框
            self.phone_input_field.returnPressed.connect(self.add_phone_number)

        # 新增登录按钮信号
        self.login_btn.clicked.connect(self.start_whatsapp_login)
        self.logout_btn.clicked.connect(self.logout_whatsapp)
        self.reset_status_btn.clicked.connect(self.reset_login_status)

        # 表格右键菜单（删除号码）
        self.phone_numbers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.phone_numbers_table.customContextMenuRequested.connect(self.show_table_context_menu)

        # 消息输入框变化时更新UI状态
        self.message_input_field.textChanged.connect(self.update_ui_state)

        # 连接UI信号到槽函数
        self.ui_signals.login_success.connect(self.show_login_success_message)
        self.ui_signals.login_error.connect(self.show_login_error_message)
        self.ui_signals.qr_code_received.connect(self.handle_qr_code_received)
    
    def init_table(self):
        """初始化电话号码表格"""
        self.phone_numbers_table.setRowCount(0)
        # 设置表格列标题（增强版）
        self.phone_numbers_table.setHorizontalHeaderLabels(["序号", "国家/地区", "电话号码", "添加时间", "状态"])

        # 设置列宽
        header = self.phone_numbers_table.horizontalHeader()
        header.setStretchLastSection(True)  # 最后一列自动拉伸
        self.phone_numbers_table.setColumnWidth(0, 60)   # 序号
        self.phone_numbers_table.setColumnWidth(1, 100)  # 国家/地区
        self.phone_numbers_table.setColumnWidth(2, 150)  # 电话号码
        self.phone_numbers_table.setColumnWidth(3, 120)  # 添加时间

        self.update_table_display()

    def load_phone_numbers_from_db(self):
        """从数据库加载电话号码"""
        try:
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            logger.info(f"从数据库加载了 {len(phone_records)} 个电话号码")
            self.update_table_display()
        except Exception as e:
            logger.error(f"从数据库加载电话号码时出错: {str(e)}")

    def update_ui_state(self):
        """更新UI状态"""
        try:
            # 检查登录状态
            is_logged_in = self.login_manager.check_login_status()
            is_logging_in = self.login_manager.is_login_in_progress()

            # 检查数据状态
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            has_numbers = len(phone_records) > 0
            has_message = len(self.message_input_field.toPlainText().strip()) > 0

            # 检查发送状态
            is_sending = self.bulk_sender.is_sending_in_progress()

            # 调试信息
            logger.debug(f"UI状态更新: 登录={is_logged_in}, 登录中={is_logging_in}, 号码数={len(phone_records)}, 有消息={has_message}, 发送中={is_sending}")

            # 更新登录相关按钮
            self.login_btn.setEnabled(not is_logged_in and not is_logging_in)
            self.logout_btn.setEnabled(is_logged_in)

            # 更新登录状态显示
            if is_logged_in:
                self.status_indicator.setStyleSheet("color: #4CAF50; font-size: 16pt;")  # 绿色
                self.status_text.setText("已登录")
                self.login_info.setText("WhatsApp已登录，可以开始群发消息")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")
            elif is_logging_in:
                self.status_indicator.setStyleSheet("color: #ffa726; font-size: 16pt;")  # 橙色
                self.status_text.setText("登录中...")
                self.login_info.setText("正在登录WhatsApp，请扫描二维码")
                self.login_info.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            else:
                self.status_indicator.setStyleSheet("color: #ff4444; font-size: 16pt;")  # 红色
                self.status_text.setText("未登录")
                self.login_info.setText("请先登录WhatsApp才能使用群发功能")
                self.login_info.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")

            # 更新功能按钮状态
            self.add_phone_btn.setEnabled(not is_sending)

            # 群发按钮启用条件
            can_send = is_logged_in and has_numbers and has_message and not is_sending
            self.start_bulk_send_btn.setEnabled(can_send)

            # 调试群发按钮状态
            if not can_send:
                reasons = []
                if not is_logged_in:
                    reasons.append("未登录")
                if not has_numbers:
                    reasons.append("无电话号码")
                if not has_message:
                    reasons.append("无消息内容")
                if is_sending:
                    reasons.append("正在发送中")
                logger.debug(f"群发按钮禁用原因: {', '.join(reasons)}")

            # 更新按钮文本
            if is_sending:
                self.start_bulk_send_btn.setText("发送中...")
            else:
                self.start_bulk_send_btn.setText("开始群发")

        except Exception as e:
            logger.error(f"更新UI状态时出错: {str(e)}")

    def update_ui_state_timer(self):
        """定时器触发的UI状态更新（减少日志输出）"""
        try:
            # 检查登录状态
            is_logged_in = self.login_manager.check_login_status()
            is_logging_in = self.login_manager.is_login_in_progress()

            # 检查数据状态
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            has_numbers = len(phone_records) > 0
            has_message = len(self.message_input_field.toPlainText().strip()) > 0

            # 检查发送状态
            is_sending = self.bulk_sender.is_sending_in_progress()

            # 更新登录相关按钮
            self.login_btn.setEnabled(not is_logged_in and not is_logging_in)
            self.logout_btn.setEnabled(is_logged_in)

            # 更新登录状态显示
            if is_logged_in:
                self.status_indicator.setStyleSheet("color: #4CAF50; font-size: 16pt;")  # 绿色
                self.status_text.setText("已登录")
                if not hasattr(self, '_login_info_updated') or not self._login_info_updated:
                    self.login_info.setText("WhatsApp已登录，可以开始群发消息")
                    self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")
            elif is_logging_in:
                self.status_indicator.setStyleSheet("color: #ffa726; font-size: 16pt;")  # 橙色
                self.status_text.setText("登录中...")
                self.login_info.setText("正在登录WhatsApp，请扫描二维码")
                self.login_info.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")
            else:
                self.status_indicator.setStyleSheet("color: #ff4444; font-size: 16pt;")  # 红色
                self.status_text.setText("未登录")
                self.login_info.setText("请先登录WhatsApp才能使用群发功能")
                self.login_info.setStyleSheet("color: #ffa726; font-size: 10pt; font-style: italic;")

            # 更新功能按钮状态
            self.add_phone_btn.setEnabled(not is_sending)

            # 群发按钮启用条件
            can_send = is_logged_in and has_numbers and has_message and not is_sending
            self.start_bulk_send_btn.setEnabled(can_send)

            # 更新按钮文本
            if is_sending:
                self.start_bulk_send_btn.setText("发送中...")
            else:
                self.start_bulk_send_btn.setText("开始群发")

        except Exception as e:
            logger.error(f"定时器更新UI状态时出错: {str(e)}")

    def validate_phone_number(self, phone):
        """验证电话号码格式并转换为WhatsApp格式"""
        # 移除所有空格和特殊字符，只保留数字和+号
        cleaned_phone = re.sub(r'[^\d+]', '', phone.strip())

        # 检查是否为空
        if not cleaned_phone:
            return False, "电话号码不能为空"

        # 移除+号，统一处理
        if cleaned_phone.startswith('+'):
            cleaned_phone = cleaned_phone[1:]

        # 验证和格式化不同类型的号码
        if len(cleaned_phone) == 11 and cleaned_phone.startswith(('13', '14', '15', '16', '17', '18', '19')):
            # 中国手机号码（11位）
            whatsapp_format = f"86{cleaned_phone}@c.us"
            display_format = f"+86 {cleaned_phone[:3]} {cleaned_phone[3:7]} {cleaned_phone[7:]}"
            return True, {"whatsapp": whatsapp_format, "display": display_format, "original": cleaned_phone}

        elif len(cleaned_phone) >= 10 and len(cleaned_phone) <= 15:
            # 国际号码
            if cleaned_phone.startswith('86') and len(cleaned_phone) == 13:
                # 已经包含86国家代码的中国号码
                mobile_part = cleaned_phone[2:]
                if mobile_part.startswith(('13', '14', '15', '16', '17', '18', '19')):
                    whatsapp_format = f"{cleaned_phone}@c.us"
                    display_format = f"+86 {mobile_part[:3]} {mobile_part[3:7]} {mobile_part[7:]}"
                    return True, {"whatsapp": whatsapp_format, "display": display_format, "original": cleaned_phone}

            # 其他国际号码
            whatsapp_format = f"{cleaned_phone}@c.us"
            display_format = f"+{cleaned_phone}"
            return True, {"whatsapp": whatsapp_format, "display": display_format, "original": cleaned_phone}

        else:
            return False, "电话号码格式不正确，请输入有效的手机号码"

    def format_phone_for_display(self, whatsapp_format):
        """将WhatsApp格式的电话号码转换为用户友好的显示格式"""
        try:
            # 移除@c.us后缀
            if whatsapp_format.endswith('@c.us'):
                phone_number = whatsapp_format[:-5]
            else:
                phone_number = whatsapp_format

            # 处理中国号码
            if phone_number.startswith('86') and len(phone_number) == 13:
                mobile_part = phone_number[2:]
                if mobile_part.startswith(('13', '14', '15', '16', '17', '18', '19')):
                    return f"+86 {mobile_part[:3]} {mobile_part[3:7]} {mobile_part[7:]}"

            # 处理其他国际号码
            if len(phone_number) >= 10:
                return f"+{phone_number}"

            # 如果无法识别格式，返回原始格式
            return whatsapp_format

        except Exception as e:
            logger.error(f"格式化电话号码显示时出错: {str(e)}")
            return whatsapp_format

    def add_phone_number(self):
        """添加电话号码到数据库"""
        # 检查是否使用新的国际电话号码输入组件
        if hasattr(self, 'international_phone_input'):
            phone_data = self.international_phone_input.get_phone_data()
            if not phone_data:
                QMessageBox.warning(self.main_window, "输入错误", "请输入有效的电话号码")
                return

            whatsapp_format = phone_data["whatsapp_format"]
            display_format = phone_data["display_format"]
            country_info = phone_data["country_info"]

        else:
            # 回退到原有逻辑
            phone = self.phone_input_field.text().strip()
            if not phone:
                QMessageBox.warning(self.main_window, "输入错误", "请输入电话号码")
                return

            # 验证电话号码格式
            is_valid, result = self.validate_phone_number(phone)
            if not is_valid:
                QMessageBox.warning(self.main_window, "格式错误", result)
                return

            # result现在是一个包含不同格式的字典
            phone_formats = result
            whatsapp_format = phone_formats["whatsapp"]  # 用于发送的格式
            display_format = phone_formats["display"]    # 用于显示的格式
            country_info = None

        # 添加到数据库（存储WhatsApp格式，用于实际发送）
        record_id = self.telenumber_ops.add_phone_number(whatsapp_format, self.username, self.store_name)
        if record_id == -1:
            QMessageBox.warning(self.main_window, "重复号码", "该电话号码已存在")
            return

        # 清空输入框
        if hasattr(self, 'international_phone_input'):
            self.international_phone_input.clear()
        else:
            self.phone_input_field.clear()

        # 更新表格显示
        self.update_table_display()

        # 更新UI状态
        self.update_ui_state()

        # 记录日志
        country_name = country_info["name_cn"] if country_info else "未知"
        logger.info(f"添加电话号码到数据库: {display_format} ({country_name}) -> {whatsapp_format} (ID: {record_id})")
    
    def update_table_display(self):
        """更新表格显示（增强版）"""
        try:
            # 从数据库获取电话号码
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)

            self.phone_numbers_table.setRowCount(len(phone_records))

            for i, record in enumerate(phone_records):
                # 序号
                item_index = QTableWidgetItem(str(i + 1))
                item_index.setTextAlignment(Qt.AlignCenter)
                # 存储记录ID用于删除操作
                item_index.setData(Qt.UserRole, record['id'])
                self.phone_numbers_table.setItem(i, 0, item_index)

                # 解析国家信息
                country_info = self._parse_country_from_phone(record['phone_number'])

                # 国家/地区
                if country_info:
                    country_text = f"{country_info['flag']} {country_info['name_cn']}"
                else:
                    country_text = "🌍 未知"
                item_country = QTableWidgetItem(country_text)
                item_country.setTextAlignment(Qt.AlignCenter)
                self.phone_numbers_table.setItem(i, 1, item_country)

                # 电话号码（转换为用户友好的显示格式）
                display_phone = self.format_phone_for_display(record['phone_number'])
                item_phone = QTableWidgetItem(display_phone)
                item_phone.setTextAlignment(Qt.AlignCenter)
                # 存储原始的WhatsApp格式用于发送
                item_phone.setData(Qt.UserRole, record['phone_number'])
                self.phone_numbers_table.setItem(i, 2, item_phone)

                # 添加时间
                created_time = record.get('created_at', '')
                if created_time:
                    try:
                        # 尝试格式化时间
                        if isinstance(created_time, str):
                            time_display = created_time[:16]  # 只显示到分钟
                        else:
                            time_display = str(created_time)[:16]
                    except:
                        time_display = "未知"
                else:
                    time_display = "未知"

                item_time = QTableWidgetItem(time_display)
                item_time.setTextAlignment(Qt.AlignCenter)
                self.phone_numbers_table.setItem(i, 3, item_time)

                # 状态（翻译为中文）
                status_map = {
                    'pending': '待发送',
                    'sending': '发送中',
                    'sent': '已发送',
                    'failed': '发送失败',
                    'blocked': '已拉黑'
                }
                status_text = status_map.get(record['status'], record['status'])
                item_status = QTableWidgetItem(status_text)
                item_status.setTextAlignment(Qt.AlignCenter)

                # 根据状态设置颜色
                if record['status'] == 'sent':
                    item_status.setForeground(Qt.green)
                elif record['status'] == 'failed':
                    item_status.setForeground(Qt.red)
                elif record['status'] == 'sending':
                    item_status.setForeground(Qt.yellow)
                elif record['status'] == 'blocked':
                    item_status.setForeground(Qt.darkRed)

                self.phone_numbers_table.setItem(i, 4, item_status)

        except Exception as e:
            logger.error(f"更新表格显示时出错: {str(e)}")

    def _parse_country_from_phone(self, whatsapp_format):
        """从WhatsApp格式的电话号码解析国家信息"""
        try:
            if whatsapp_format.endswith('@c.us'):
                phone_number = whatsapp_format[:-5]
                parsed = self.country_manager.parse_international_number(phone_number)
                if parsed:
                    country_code, _ = parsed
                    return self.country_manager.get_country_info(country_code)
        except Exception as e:
            logger.error(f"解析国家信息时出错: {str(e)}")
        return None
    
    def start_bulk_send(self):
        """开始群发消息"""
        try:
            # 检查登录状态
            if not self.login_manager.check_login_status():
                QMessageBox.warning(self.main_window, "未登录", "请先登录WhatsApp")
                return

            # 获取电话号码列表
            phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
            if not phone_records:
                QMessageBox.warning(self.main_window, "无号码", "请先添加电话号码")
                return

            # 检查消息内容
            message = self.message_input_field.toPlainText().strip()
            if not message:
                QMessageBox.warning(self.main_window, "无消息", "请输入要发送的消息内容")
                return

            # 提取电话号码（已经是WhatsApp格式）
            phone_numbers = [record['phone_number'] for record in phone_records]

            # 验证所有号码都是WhatsApp格式
            for phone in phone_numbers:
                if not phone.endswith('@c.us'):
                    logger.warning(f"电话号码格式可能不正确: {phone}")

            logger.info(f"准备群发到以下号码: {phone_numbers}")

            # 确认发送
            reply = QMessageBox.question(
                self.main_window,
                "确认群发",
                f"确定要向 {len(phone_numbers)} 个号码发送消息吗？\n\n消息内容：\n{message[:100]}{'...' if len(message) > 100 else ''}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 更新所有号码状态为发送中
            for record in phone_records:
                self.telenumber_ops.update_phone_status(record['id'], 'sending')

            # 更新表格显示
            self.update_table_display()

            # 开始群发
            success = self.bulk_sender.send_bulk_messages(phone_numbers, message, delay=3000)
            if not success:
                # 如果启动失败，恢复状态
                for record in phone_records:
                    self.telenumber_ops.update_phone_status(record['id'], 'pending')
                self.update_table_display()
                return

            # 更新UI状态
            self.update_ui_state()

            logger.info(f"开始群发消息到 {len(phone_numbers)} 个号码")

        except Exception as e:
            logger.exception(f"启动群发时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"启动群发失败: {str(e)}")
    
    # ========== WhatsApp登录相关方法 ==========

    def start_whatsapp_login(self):
        """启动WhatsApp登录"""
        try:
            logger.info("用户点击登录WhatsApp按钮")
            success = self.login_manager.start_login()
            if success:
                self.update_ui_state()
                QMessageBox.information(self.main_window, "登录中", "正在启动WhatsApp登录，请在弹出的浏览器中扫描二维码")
            else:
                QMessageBox.warning(self.main_window, "登录失败", "启动登录失败，请检查网络连接")
        except Exception as e:
            logger.exception(f"启动登录时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"启动登录失败: {str(e)}")

    def logout_whatsapp(self):
        """退出WhatsApp登录"""
        try:
            logger.info("用户点击退出登录按钮")
            success = self.login_manager.logout()
            if success:
                self.update_ui_state()
                QMessageBox.information(self.main_window, "已退出", "已退出WhatsApp登录")
            else:
                QMessageBox.warning(self.main_window, "退出失败", "退出登录失败")
        except Exception as e:
            logger.exception(f"退出登录时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"退出登录失败: {str(e)}")

    def reset_login_status(self):
        """重置登录状态"""
        try:
            logger.info("用户点击重置登录状态按钮")

            # 强制退出当前登录
            self.login_manager.logout()

            # 清理状态文件
            import os
            login_status_file = self.login_manager.login_status_file
            if os.path.exists(login_status_file):
                with open(login_status_file, "w", encoding="utf-8") as f:
                    import json
                    json.dump({"status": "initializing"}, f)
                logger.info("已清理登录状态文件")

            # 重置内存状态
            self.login_manager.is_logged_in = False
            self.login_manager.is_logging_in = False

            # 更新UI
            self.update_ui_state()

            QMessageBox.information(self.main_window, "重置完成", "登录状态已重置，可以重新尝试登录")

        except Exception as e:
            logger.exception(f"重置登录状态时出错: {str(e)}")
            QMessageBox.critical(self.main_window, "错误", f"重置状态失败: {str(e)}")

    # ========== 线程安全的回调函数 ==========

    def on_login_status_changed_safe(self, is_logged_in):
        """线程安全的登录状态变化回调"""
        logger.info(f"登录状态变化: {is_logged_in}")
        # 使用信号来安全地更新UI
        if is_logged_in:
            self.ui_signals.login_success.emit()
        # 总是更新UI状态
        self.update_ui_state()

    def on_qr_code_received_safe(self, qr_code):
        """线程安全的QR码接收回调"""
        logger.info("收到WhatsApp登录二维码")
        # 使用信号来安全地处理QR码
        self.ui_signals.qr_code_received.emit(qr_code)
        self.update_ui_state()

    def on_login_error_safe(self, error_message):
        """线程安全的登录错误回调"""
        logger.error(f"登录错误: {error_message}")
        # 使用信号来安全地显示错误
        self.ui_signals.login_error.emit(error_message)
        self.update_ui_state()

    # ========== UI信号处理函数（在主线程中执行）==========

    def show_login_success_message(self):
        """显示登录成功消息（主线程安全）"""
        try:
            # 不显示弹窗，只更新状态文本和日志
            logger.info("WhatsApp登录成功，群发功能已可用")

            # 更新登录信息文本为成功状态
            if hasattr(self, 'login_info'):
                self.login_info.setText("✅ WhatsApp登录成功！群发功能已可用")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-weight: bold;")

            # 可以添加一个临时的成功提示，几秒后自动消失
            self.show_temporary_success_message()

        except Exception as e:
            logger.error(f"显示登录成功消息时出错: {str(e)}")

    def show_temporary_success_message(self):
        """显示临时成功消息"""
        try:
            # 创建一个定时器来显示临时消息
            if hasattr(self, 'success_timer'):
                self.success_timer.stop()

            self.success_timer = QTimer()
            self.success_timer.setSingleShot(True)
            self.success_timer.timeout.connect(self.hide_temporary_success_message)

            # 显示成功消息3秒
            if hasattr(self, 'login_info'):
                self.login_info.setText("🎉 WhatsApp登录成功！现在可以开始群发消息了")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-weight: bold; background-color: rgba(76, 175, 80, 0.1); padding: 5px; border-radius: 3px;")

            self.success_timer.start(3000)  # 3秒后恢复正常状态

        except Exception as e:
            logger.error(f"显示临时成功消息时出错: {str(e)}")

    def hide_temporary_success_message(self):
        """隐藏临时成功消息"""
        try:
            if hasattr(self, 'login_info'):
                self.login_info.setText("WhatsApp已登录，可以开始群发消息")
                self.login_info.setStyleSheet("color: #4CAF50; font-size: 10pt; font-style: italic;")
        except Exception as e:
            logger.error(f"隐藏临时成功消息时出错: {str(e)}")

    def show_login_error_message(self, error_message):
        """显示登录错误消息（主线程安全）"""
        try:
            QMessageBox.critical(self.main_window, "登录错误", f"WhatsApp登录失败:\n{error_message}")
        except Exception as e:
            logger.error(f"显示登录错误消息时出错: {str(e)}")

    def handle_qr_code_received(self, qr_code):
        """处理QR码接收（主线程安全）"""
        try:
            # QR码已经在终端显示，这里可以添加其他处理
            logger.info("QR码已生成，请在浏览器中扫描")
        except Exception as e:
            logger.error(f"处理QR码时出错: {str(e)}")

    # ========== 保留原有的回调函数（用于兼容性）==========

    def on_login_status_changed(self, is_logged_in):
        """登录状态变化回调（兼容性保留）"""
        self.on_login_status_changed_safe(is_logged_in)

    def on_qr_code_received(self, qr_code):
        """收到QR码回调（兼容性保留）"""
        self.on_qr_code_received_safe(qr_code)

    def on_login_error(self, error_message):
        """登录错误回调（兼容性保留）"""
        self.on_login_error_safe(error_message)

    # ========== 群发发送相关方法 ==========

    def on_send_progress(self, progress, status_message, current_phone):
        """发送进度回调"""
        logger.info(f"发送进度: {progress}% - {current_phone}")

        # 更新当前发送号码的状态
        if current_phone:
            self.telenumber_ops.update_phone_status_by_number(
                current_phone, self.username, 'sending', self.store_name
            )

        # 更新表格显示
        self.update_table_display()

        # 更新按钮文本显示进度
        self.start_bulk_send_btn.setText(f"发送中... {progress}%")

    def on_send_completed(self, success, sent_count, total_count, errors):
        """发送完成回调"""
        logger.info(f"群发完成: 成功 {sent_count}/{total_count}")

        # 更新所有号码的状态
        phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)

        # 创建错误号码集合
        error_phones = {error.get('phone', '') for error in errors}

        for record in phone_records:
            if record['phone_number'] in error_phones:
                self.telenumber_ops.update_phone_status(record['id'], 'failed')
            else:
                self.telenumber_ops.update_phone_status(record['id'], 'sent')

        # 更新UI
        self.update_table_display()
        self.update_ui_state()

        # 显示结果
        if errors:
            error_details = "\n".join([f"• {error.get('phone', '')}: {error.get('error', '')}" for error in errors[:5]])
            if len(errors) > 5:
                error_details += f"\n... 还有 {len(errors) - 5} 个错误"

            QMessageBox.warning(
                self.main_window,
                "群发完成（有错误）",
                f"群发完成！\n\n成功发送: {sent_count}/{total_count}\n失败: {len(errors)}\n\n失败详情:\n{error_details}"
            )
        else:
            QMessageBox.information(
                self.main_window,
                "群发完成",
                f"群发完成！\n\n全部发送成功: {sent_count}/{total_count}"
            )

    def on_send_error(self, error_message):
        """发送错误回调"""
        logger.error(f"群发错误: {error_message}")

        # 恢复所有号码状态为待发送
        phone_records = self.telenumber_ops.get_phone_numbers_by_user(self.username, self.store_name)
        for record in phone_records:
            if record['status'] == 'sending':
                self.telenumber_ops.update_phone_status(record['id'], 'pending')

        # 更新UI
        self.update_table_display()
        self.update_ui_state()

        QMessageBox.critical(self.main_window, "发送错误", f"群发过程中出错:\n{error_message}")

    # ========== 表格操作相关方法 ==========

    def show_table_context_menu(self, position):
        """显示表格右键菜单"""
        try:
            from PySide6.QtWidgets import QMenu

            item = self.phone_numbers_table.itemAt(position)
            if item is None:
                return

            row = item.row()
            if row < 0:
                return

            # 创建右键菜单
            menu = QMenu(self.phone_numbers_table)

            delete_action = menu.addAction("删除号码")
            reset_action = menu.addAction("重置状态")

            # 显示菜单
            action = menu.exec_(self.phone_numbers_table.mapToGlobal(position))

            if action == delete_action:
                self.delete_phone_number_at_row(row)
            elif action == reset_action:
                self.reset_phone_status_at_row(row)

        except Exception as e:
            logger.error(f"显示右键菜单时出错: {str(e)}")

    def delete_phone_number_at_row(self, row):
        """删除指定行的电话号码"""
        try:
            # 获取记录ID
            item = self.phone_numbers_table.item(row, 0)
            if not item:
                return

            record_id = item.data(Qt.UserRole)
            if not record_id:
                return

            # 获取电话号码用于确认
            phone_item = self.phone_numbers_table.item(row, 1)
            phone_number = phone_item.text() if phone_item else "未知号码"

            # 确认删除
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除电话号码 {phone_number} 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success = self.telenumber_ops.delete_phone_number(record_id)
                if success:
                    self.update_table_display()
                    self.update_ui_state()
                    logger.info(f"删除电话号码: {phone_number}")
                else:
                    QMessageBox.warning(self.main_window, "删除失败", "删除电话号码失败")

        except Exception as e:
            logger.error(f"删除电话号码时出错: {str(e)}")

    def reset_phone_status_at_row(self, row):
        """重置指定行的电话号码状态"""
        try:
            # 获取记录ID
            item = self.phone_numbers_table.item(row, 0)
            if not item:
                return

            record_id = item.data(Qt.UserRole)
            if not record_id:
                return

            # 重置状态为待发送
            success = self.telenumber_ops.update_phone_status(record_id, 'pending')
            if success:
                self.update_table_display()
                logger.info(f"重置电话号码状态: ID {record_id}")
            else:
                QMessageBox.warning(self.main_window, "重置失败", "重置状态失败")

        except Exception as e:
            logger.error(f"重置电话号码状态时出错: {str(e)}")

    def clear_phone_numbers(self):
        """清空电话号码列表"""
        try:
            reply = QMessageBox.question(
                self.main_window,
                "确认清空",
                "确定要清空所有电话号码吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success = self.telenumber_ops.clear_phone_numbers(self.username, self.store_name)
                if success:
                    self.update_table_display()
                    self.update_ui_state()
                    logger.info("清空电话号码列表")
                else:
                    QMessageBox.warning(self.main_window, "清空失败", "清空电话号码失败")

        except Exception as e:
            logger.error(f"清空电话号码时出错: {str(e)}")
